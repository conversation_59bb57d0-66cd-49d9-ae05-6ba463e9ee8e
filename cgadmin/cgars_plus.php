<?php
/*
  $Id: customers_points.php, V2.1rc2a 2008/SEP/29 09:00:46 dsa_ Exp $
  created by <PERSON>, Deep Silver Accessories
  http://www.deep-silver.com

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2005 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');

  require(DIR_WS_CLASSES . 'currencies.php');
  $currencies = new currencies();

  // Define MAX_DISPLAY_SEARCH_RESULTS if not already defined
  if (!defined('MAX_DISPLAY_SEARCH_RESULTS')) {
    define('MAX_DISPLAY_SEARCH_RESULTS', 20);
  }

  $action = (isset($_GET['action']) ? $_GET['action'] : '');

  // Date range filtering logic
  if (isset($_GET["date1"]) && !empty($_GET["date1"]) && isset($_GET["date2"]) && !empty($_GET["date2"])) {
    $date1 = $_GET["date1"];
    $date2 = $_GET["date2"];
  } else {
    $date1 = '';
    $date2 = '';
  }

  // Validate date format
  if (!empty($date1) && !preg_match("/([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})/", $date1)) {
    $date1 = '';
  }
  if (!empty($date2) && !preg_match("/([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})/", $date2)) {
    $date2 = '';
  }

  $cal1maxdate = date("Y") . "," . date("m") . "," . date("d");


  if (tep_not_null($action)) {
    switch ($action) {
      case 'addconfirm':

        $comment = tep_db_prepare_input($_POST['comment']);
        $customers_id = tep_db_prepare_input($_GET['cID']);
        $pointstoadd = tep_db_prepare_input($_POST['points_to_add']);

        $pointstoadd = number_format(abs($pointstoadd), 2, '.', '');
        
        if(!is_numeric($customers_id) || !is_numeric($pointstoadd)){
          $messageStack->add_session(WARNING_DATABASE_NOT_UPDATED, 'warning');
          tep_redirect(tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('oID', 'action'))));      
        }

        $orders_id = 0;
        $points_added = false;

        // Get Loyalty Settings
        $loyalty_settings = tep_loyalty_scheme_logic($customers_id);
        $new_points_total = ($loyalty_settings['account_cg_plus_points_total'] + $pointstoadd);

  if ($pointstoadd > 0) {

	      tep_db_query("update " . TABLE_CUSTOMERS . " set customers_cgars_plus = '". tep_db_input($new_points_total) ."' where customers_id = '". (int)$customers_id ."'");

		    if (isset($_POST['add_review']) && ($_POST['add_review'] == 'on')) {
		     tep_db_query("update " . TABLE_CUSTOMERS . " set customers_points_review = '1' where customers_id = '". (int)$customers_id ."'");
		    }

    $customer_notified = '0';
    if (isset($_POST['notify']) && ($_POST['notify'] == 'on')) {
            
            $balance = $new_points_total;
            $gender = $_POST['customers_gender'];
            $first_name = $_POST['customers_firstname'];
            $last_name = $_POST['customers_lastname'];
            $name = $first_name . ' ' . $last_name;
			      $customers_email_address = $_POST['customers_email_address'];

            $notify_comment = '';
            if (isset($_POST['comment']) && tep_not_null($comment)) {
              $notify_comment =  '<br/><br/>' . $comment;
            }

   $email_generic_heading = 'C.Gars Plus Update';
   $email_generic_sub_heading = 'Your C.Gars Plus Account has been updated.';

   $email_generic_body_text = '<h4 class="mb_xxs mte" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 16px;margin-bottom: 4px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Dear ' . $first_name . ',</h4><br />This is to inform you that your C.Gars Plus Account has been updated.<br/><br/>';

   $email_generic_body_text .= 'We have credited your account with total of <strong>' . $pointstoadd . '</strong> points valued at <strong>' . $currencies->format($pointstoadd * CGARS_PLUS_REDEEM_POINT_VALUE) . '</strong><br/><br/>Your current C.Gars Plus balance is: <strong>' . round($balance) . '</strong> points valued at <strong>' . $currencies->format($balance * CGARS_PLUS_REDEEM_POINT_VALUE) . '</strong><br/><br/>';

   $email_generic_body_text .= 'Your points are available to spend during the checkout procces. For help with any of our online services, please email us at: <a href="mailto:' . STORE_OWNER_EMAIL_ADDRESS . '">' . STORE_OWNER_EMAIL_ADDRESS . '</a><br/><br/>We look forward to seeing you again soon.';

   if($notify_comment){
    $email_generic_body_text .= '<br /><br /><strong>Additional Comments:</strong>'; 
    $email_generic_body_text .= $notify_comment;
   }

   // load email templates
   include('../' . DIR_WS_LANGUAGES . 'english/email_generic_template.php');

   $email_text  = EMAIL_GENERIC_TEMPLATE_START;
   $email_text .= EMAIL_GENERIC_TEMPLATE_HEADER;
   $email_text .= $email_generic_body_text;
   $email_text .= EMAIL_GENERIC_TEMPLATE_FOOTER;
   $email_text .= EMAIL_GENERIC_TEMPLATE_END;

   tep_mail($name, $customers_email_address, EMAIL_TEXT_SUBJECT, $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

   $customer_notified = '1';
   $messageStack->add_session(sprintf(NOTICE_EMAIL_SENT_TO, $name . '(' . $customers_email_address . ').'), 'success');

   } // end notify customer

   if(!tep_not_null($comment)){
    $comment = 'Points added by admin ' . $admin['id'];
   }

   // Add to points pending table
   $sql_data_array = array('customer_id' => (int)$customers_id,
   'orders_id' => $orders_id,
   'points_comment' => tep_db_input($comment),
   'points_pending' => $pointstoadd,
   'remaining' => $pointstoadd,
   'date_added' => 'now()',
   'points_status' => 2);

   tep_db_perform(TABLE_CGARS_PLUS, $sql_data_array);

   $points_added = true;

   // tep_cgars_plus_set_member_level($customers_id); // set membership level

   }

        if ($points_added == true) {
          $messageStack->add_session(SUCCESS_POINTS_UPDATED, 'success');
        } else {
          $messageStack->add_session(WARNING_DATABASE_NOT_UPDATED, 'warning');
        }
        tep_redirect(tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('oID', 'action'))));
        break;
        case 'signupconfirm':
        
          $customers_id = tep_db_prepare_input($_GET['cID']);

          if(!is_numeric($customers_id)){
            $messageStack->add_session(WARNING_DATABASE_NOT_UPDATED, 'warning');
            tep_redirect(tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('oID', 'action'))));      
          }
  
          tep_cgars_plus_member_status((int)$customers_id, 'enable'); // adds cgars plus table entry

          if (isset($_POST['bonus']) && ($_POST['bonus'] == 'on')) {

            $customers_query = tep_db_query("select c.customers_lastname, c.customers_firstname, c.customers_email_address from " . TABLE_CUSTOMERS . " c where c.customers_id = '" . $customers_id . "'");
            while ($customers = tep_db_fetch_array($customers_query)) {
              tep_cp_send_welcome_email($customers['customers_email_address'], $customers['customers_firstname'] . ' ' . $customers['customers_lastname']);
              $messageStack->add_session('Customer signed up and welcome email sent', 'success');
              if (MAILCHIMP_ENABLED == 'true') {
                require_once('../' . DIR_WS_MODULES . 'mailchimp/mailchimpRoot.php');
                $mailchimp = new Mailchimp(MAILCHIMP_API_KEY);
                $merge_values = array('FNAME'=> $customers['customers_firstname'], 'LNAME'=> $customers['customers_lastname']);
                $post_params = array('email_address' => $customers['customers_email_address'], 'status' => 'subscribed', 'email_type' => 'html', 'merge_fields' => $merge_values);
                $mailchimp->lists('b99400d7f0')->members()->POST($post_params); // CGars Plus
              } 
            }
            //tep_cp_add_bonus_points((int)$customers_id, NEW_SIGNUP_CGARS_PLUS_POINT_AMOUNT, 'Customer sign up bonus'); // adds bonus
          } else {
            $messageStack->add_session('Customer signed up', 'success');
          }
 
          tep_redirect(tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('oID', 'action'))));
          break;
      case 'delconfirm':
        
        $customers_id = tep_db_prepare_input($_GET['cID']);
        $comment = tep_db_prepare_input($_POST['comment']);
        $pointstodel = tep_db_prepare_input($_POST['points_to_delete']);

        $pointstodel = number_format(abs($pointstodel), 2, '.', '');

        if(!is_numeric($customers_id) || !is_numeric($pointstodel)){
          $messageStack->add_session(WARNING_DATABASE_NOT_UPDATED, 'warning');
          tep_redirect(tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('oID', 'action'))));      
        }

        $orders_id = 0;
        $points_deleted = false;

        // Get Loyalty Settings
        $loyalty_settings = tep_loyalty_scheme_logic($customers_id);

        $new_points_total = ($loyalty_settings['account_cg_plus_points_total'] - $pointstodel);

        if($new_points_total < 0){ 
          $new_points_total = 0; 
          $pointstodel = $loyalty_settings['account_cg_plus_points_total'];
        }
    
    if ($pointstodel > 0) {
          
	      tep_db_query("update " . TABLE_CUSTOMERS . " set customers_cgars_plus = '". tep_db_input($new_points_total) ."' where customers_id = '". (int)$customers_id ."'");

        $customer_notified = '0';

    if (isset($_POST['notify']) && ($_POST['notify'] == 'on')) {

            $balance = $new_points_total;      
            $gender = $_POST['customers_gender'];
            $first_name = $_POST['customers_firstname'];
            $last_name = $_POST['customers_lastname'];
            $name = $first_name . ' ' . $last_name;
			      $customers_email_address = $_POST['customers_email_address'];

            $notify_comment = '';
            if (isset($_POST['comment']) && tep_not_null($comment)) {
              $notify_comment =  '<br/><br/>' . $comment;
            }

            if ($balance> 0) {
              $customer_balance = sprintf(EMAIL_TEXT_BALANCE, number_format($balance,CGARS_PLUS_POINTS_DECIMAL_PLACES), $currencies->format($balance * CGARS_PLUS_REDEEM_POINT_VALUE));
            }

   $email_generic_heading = 'C.Gars Plus Update';
   $email_generic_sub_heading = 'Your C.Gars Plus Account has been updated.';

   // load email templates
   include('../' . DIR_WS_LANGUAGES . 'english/email_generic_template.php');

   $email_generic_body_text = '<h4 class="mb_xxs mte" style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 16px;margin-bottom: 4px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Dear ' . $first_name . ',</h4><br />This is to inform you that your C.Gars Plus Account has been updated.<br/><br/>';

   $email_generic_body_text .= 'We have deducted a total of <strong>' . $pointstodel . '</strong> points valued at <strong>' . $currencies->format($pointstodel * CGARS_PLUS_REDEEM_POINT_VALUE) . '</strong> from your account.<br/><br/>Your current C.Gars Plus balance is: <strong>' . round($balance) . '</strong> points valued at <strong>' . $currencies->format($balance * CGARS_PLUS_REDEEM_POINT_VALUE) . '</strong><br/><br/>';

   $email_generic_body_text .= 'Your points are available to spend during the checkout procces. For help with any of our online services, please email us at: <a href="mailto:' . STORE_OWNER_EMAIL_ADDRESS . '">' . STORE_OWNER_EMAIL_ADDRESS . '</a><br/><br/>We look forward to seeing you again soon.';

   if($notify_comment){
     $email_generic_body_text .= '<br /><br /><strong>Additional Comments:</strong>'; 
     $email_generic_body_text .= $notify_comment;
   }

   $email_text  = EMAIL_GENERIC_TEMPLATE_START;
   $email_text .= EMAIL_GENERIC_TEMPLATE_HEADER;
   $email_text .= $email_generic_body_text;
   $email_text .= EMAIL_GENERIC_TEMPLATE_FOOTER;
   $email_text .= EMAIL_GENERIC_TEMPLATE_END;

   tep_mail($name, $customers_email_address, EMAIL_TEXT_SUBJECT, $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

   $customer_notified = '1';
   $messageStack->add_session(sprintf(NOTICE_EMAIL_SENT_TO, $name . '(' . $customers_email_address . ').'), 'success');
          
  } // end notify custmer
         
  if(!tep_not_null($comment)){
   $comment = 'Points removed by admin ' . $admin['id'];
  }

            $sql_data_array = array('customer_id' => $customers_id,
                                    'orders_id' => $orders_id,
                                    'points_comment' => tep_db_input($comment),
                                    'points_pending' => -$pointstodel,
                                    'date_added' => 'now()',
                                    'points_status' => 3);

			      tep_db_perform(TABLE_CGARS_PLUS, $sql_data_array);

            $points_added = true;

            tep_cp_redeem_deduct_remaining($customers_id, $pointstodel); // set new remaining figures

            tep_cgars_plus_set_member_level($customers_id); // set membership level

  } // end if ($pointstodel > 0)
        
        if ($points_added == true) {
         $messageStack->add_session(SUCCESS_POINTS_UPDATED, 'success');
        } else {
          $messageStack->add_session(WARNING_DATABASE_NOT_UPDATED, 'warning');
        }
        tep_redirect(tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('oID', 'action'))));
        break;
    }
  }

//drop-down filter array
  $filter_array = array( array('id' => '1', 'text' => 'Enrolled'),
                         array('id' => '2', 'text' => TEXT_SORT_POINTS),
                         array('id' => '3', 'text' => TEXT_SORT_NO_POINTS),
                         array('id' => '4', 'text' => 'Bronze Members'),
                         array('id' => '5', 'text' => 'Silver Members'),
                         array('id' => '6', 'text' => 'Gold Members'),
                         array('id' => '7', 'text' => TEXT_SHOW_ALL));

  $point_or_points = ((CGARS_PLUS_POINTS_PER_AMOUNT_PURCHASE > 1) ? HEADING_POINTS : HEADING_POINT);

  require(DIR_WS_INCLUDES . 'template_top.php');

?>
<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
<div id="spiffycalendar" class="text"></div>
<script language="javascript"><!--
  var cal11 = new ctlSpiffyCalendarBox("cal11", "datefilterform", "date1", "btnDate1", "", scBTNMODE_CALBTN);
  cal11.readonly = true;
  cal11.displayLeft = true;
  cal11.useDateRange = true;
  cal11.setMinDate(2004, 1, 1);
  cal11.setMaxDate(<?php echo $cal1maxdate; ?>);

  var cal12 = new ctlSpiffyCalendarBox("cal12", "datefilterform", "date2", "btnDate2", "", scBTNMODE_CALBTN);
  cal12.readonly = true;
  cal12.displayLeft = true;
  cal12.useDateRange = true;
  cal12.setMinDate(2004, 1, 1);
  cal12.setMaxDate(<?php echo $cal1maxdate; ?>);

function validate(field) {
  var valid = "0123456789."
  var ok = "yes";
  var temp;
 for (var i=0; i<field.value.length; i++) {
  temp = "" + field.value.substring(i, i+1);
  if (valid.indexOf(temp) == "-1") ok = "no";
  }
  if (ok == "no") {
    alert("<?php echo POINTS_ENTER_JS_ERROR; ?>");
    field.focus();
    field.value = "";
  }
}
//--></script>
<style>
.cp_bronze {
  border-radius: 20px;
  background-color:#D49A6A;
  padding: 2px;
  width: 20px;
  color: #FFF;
}
.cp_silver {
  border-radius: 20px;
  background-color:#D9D9D9;
  padding: 2px;
  width: 20px;
  color: #333;
}
.cp_gold {
  border-radius: 20px;
  background-color:#FFD700;
  padding: 2px;
  width: 20px;
  color: #FFF;
}
</style>  
<table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td width="100%"><table border="0" width="100%" cellspacing="5" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php echo HEADING_TITLE; ?></td>
            <td align="right">
            <?php echo tep_draw_form('orders', FILENAME_CGARS_PLUS, '', 'get'); ?>
            <table border="0" width="100%" cellspacing="0" cellpadding="0">
              <tr>
                <td class="smallText" align="right"><?php echo '&nbsp;'. TEXT_SORT_CUSTOMERS . ':&nbsp;'. tep_draw_pull_down_menu('filter', $filter_array, '', 'onChange="this.form.submit();"'); ?></td>
                <td class="smallText" align="right"><?php echo 'Search name or email: ' . tep_draw_input_field('search'); ?></td>
              </tr>
            </table>
            </form>
           </td>
          </tr>
          <tr>
            <td colspan="2">
              <?php echo tep_draw_form('datefilterform', FILENAME_CGARS_PLUS, '', 'get'); ?>
              <table border="0" width="100%" cellspacing="5" cellpadding="0">
                <tr>
                  <td class="smallText">Date Range Filter:</td>
                  <td class="smallText">From:
                    <script language="javascript">
                      cal11.writeControl();
                      cal11.dateFormat = "yyyy-MM-dd";
                      <?php if (!empty($date1)) echo "datefilterform.date1.value = \"$date1\";"; ?>
                    </script>
                  </td>
                  <td class="smallText">To:
                    <script language="javascript">
                      cal12.writeControl();
                      cal12.dateFormat = "yyyy-MM-dd";
                      <?php if (!empty($date2)) echo "datefilterform.date2.value = \"$date2\";"; ?>
                    </script>
                  </td>
                  <td class="smallText">
                    <input type="submit" value="Filter">
                    <?php if (!empty($date1) || !empty($date2)) { ?>
                      <a href="<?php echo tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('date1', 'date2'))); ?>">Clear</a>
                    <?php } ?>
                  </td>
                  <?php
                  // Preserve other GET parameters
                  if (isset($_GET['filter'])) echo tep_draw_hidden_field('filter', $_GET['filter']);
                  if (isset($_GET['search'])) echo tep_draw_hidden_field('search', $_GET['search']);
                  if (isset($_GET['viewedSort'])) echo tep_draw_hidden_field('viewedSort', $_GET['viewedSort']);
                  ?>
                </tr>
              </table>
              </form>
            </td>
          </tr>
        </table></td>
      </tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr class="dataTableHeadingRow">
                <td class="dataTableHeadingContent"><?php echo '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=lastname-asc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_LASTNAME . TABLE_HEADING_SORT_UA . '">+</a>&nbsp;' . TABLE_HEADING_LASTNAME . '&nbsp;<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=lastname-desc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_LASTNAME . TABLE_HEADING_SORT_DA; ?>">-</a></td>

                <td class="dataTableHeadingContent"><?php echo '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=firstname-asc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_FIRSTNAME . TABLE_HEADING_SORT_UA . '">+</a>&nbsp;' . TABLE_HEADING_FIRSTNAME . '&nbsp;<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=firstname-desc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_FIRSTNAME . TABLE_HEADING_SORT_DA; ?>">-</a></td>

                <td class="dataTableHeadingContent" align="center"><?php echo '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=enrolled-asc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_ACTIVE . TABLE_HEADING_SORT_U1 . '">+</a>&nbsp;' . TABLE_HEADING_ACTIVE . '&nbsp;<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=enrolled-desc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_ACTIVE . TABLE_HEADING_SORT_D1; ?>">-</a></td>

                <td class="dataTableHeadingContent" align="center"><?php echo '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=points-asc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_POINTS . TABLE_HEADING_SORT_U1 . '">+</a>&nbsp;' . TABLE_HEADING_POINTS . '&nbsp;<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=points-desc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_POINTS . TABLE_HEADING_SORT_D1; ?>">-</a></td>

                <td class="dataTableHeadingContent" align="center"><?php echo '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=points-asc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_POINTS_VALUE . TABLE_HEADING_SORT_U1 . '">+</a>&nbsp;' . TABLE_HEADING_POINTS_VALUE . '&nbsp;<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=points-desc') . '" title="' . TABLE_HEADING_SORT . TABLE_HEADING_POINTS_VALUE . TABLE_HEADING_SORT_D1; ?>">-</a></td>
                <td class="dataTableHeadingContent" align="center">Member</td>
                <td class="dataTableHeadingContent" align="right"><?php echo '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=signup-asc') . '" title="Sort by Sign Up Date Ascending">+</a>&nbsp;Signed Up&nbsp;<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'viewedSort=signup-desc') . '" title="Sort by Sign Up Date Descending">-</a>'; ?></td>
                <td class="dataTableHeadingContent" align="right">Annual Spend</td>
				        <td class="dataTableHeadingContent" align="center">Trustpilot</td>
                <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
              </tr>
<?php
    $search = '';
    if (isset($_GET['search']) && tep_not_null($_GET['search'])) {

$keywords = tep_db_input(tep_db_prepare_input($_GET['search']));

$q_array = explode(' ', ($keywords));
$q_customer_firstname = '(c.customers_firstname LIKE \'%' . $q_array[0] . '%\'';
$q_customer_lastname = '(c.customers_lastname LIKE \'%' . $q_array[0] . '%\'';
$q_email = '(c.customers_email_address LIKE \'%' . $q_array[0] . '%\'';
// $q_customer_postcode = '(ab.entry_postcode LIKE \'%' . $q_array[0] . '%\'';


// more than one search term

for ($i = 1 ; $i < sizeof($q_array) ; $i++) {
$q_customer_firstname .= ' AND c.customers_lastname LIKE \'%' . $q_array[$i] . '%\''; // swapped for a reason!!!
$q_customer_lastname .= ' AND c.customers_firstname LIKE \'%' . $q_array[$i] . '%\'';
$q_email .= ' AND c.customers_email_address LIKE \'%' . $q_array[$i] . '%\'';
// $q_customer_postcode  .= ' AND ab.entry_postcode LIKE \'%' . $q_array[$i] . '%\'';

}
$q_customer_firstname .= ')';
$q_customer_lastname .= ')';
$q_email .= ')';
// $q_customer_postcode .= ')';

// $search = '(' . $q_customer_firstname . ' OR ' . $q_customer_lastname . ' OR ' . $q_email . ' OR ' . $q_customer_postcode . ')';

$search = '(' . $q_customer_firstname . ' OR ' . $q_customer_lastname . ' OR ' . $q_email . ')';

    }

 $filter = (isset($_GET['filter']) ? $_GET['filter'] : '');

 if (tep_not_null($filter)) {

 switch ($filter) {
  case '1':
    $filter = 'c.customers_cgars_plus_active > 0';
    break;
  case '2':
    $filter = "c.customers_cgars_plus > 0";
    break;
  case '3':
    $filter = "c.customers_cgars_plus = 0";
    break;
  case '4':
    $filter = "c.customers_cgars_plus_active = 1";
    break;
  case '5':
    $filter = "c.customers_cgars_plus_active = 2";
    break;
  case '6':
    $filter = "c.customers_cgars_plus_active = 3";
    break;
  case '7':
    $filter = '';
    break;
  }

 } else {
    $filter = 'c.customers_cgars_plus_active > 0';
 }

    // Build date filter
    $date_filter = '';
    if (!empty($date1) && !empty($date2)) {
      $date_filter = "DATE(cpm.member_date_created) >= DATE('" . tep_db_input($date1) . "') AND DATE(cpm.member_date_created) <= DATE('" . tep_db_input($date2) . "')";
    } elseif (!empty($date1)) {
      $date_filter = "DATE(cpm.member_date_created) >= DATE('" . tep_db_input($date1) . "')";
    } elseif (!empty($date2)) {
      $date_filter = "DATE(cpm.member_date_created) <= DATE('" . tep_db_input($date2) . "')";
    }

    $where_str = '';
    $conditions = array();

    if (tep_not_null($search)) {
      $conditions[] = $search;
    }
    if (tep_not_null($filter)) {
      $conditions[] = $filter;
    }
    if (tep_not_null($date_filter)) {
      $conditions[] = $date_filter;
    }

    if (!empty($conditions)) {
      $where_str = 'where ' . implode(' and ', $conditions) . ' ';
    }

//sort view  bof
   if (isset($_GET['viewedSort'])) {
	   $viewedSort = $_GET['viewedSort'];
   } else {
	   $viewedSort = "c.customers_lastname";
   }

   switch ($viewedSort) {
       case "lastname-asc":
         $sort .= "c.customers_lastname";
       break;
       case "lastname-desc":
         $sort .= "c.customers_lastname DESC";
       break;
       case "firstname-asc":
         $sort .= "c.customers_firstname ";
       break;
       case "firstname-desc":
         $sort .= "c.customers_firstname DESC";
       break;
       case "enrolled-asc":
        $sort .= "c.customers_cgars_plus_active ";
      break;
      case "enrolled-desc":
        $sort .= "c.customers_cgars_plus_active DESC";
      break;
       case "points-asc":
         $sort .= "c.customers_cgars_plus";
       break;
       case "points-desc":
         $sort .= "c.customers_cgars_plus DESC";
       break;
       case "signup-asc":
         $sort .= "cpm.member_date_created ASC";
       break;
       case "signup-desc":
         $sort .= "cpm.member_date_created DESC";
       break;
        default:
        $sort .= "c.customers_lastname";
   }
//sort view  bof

   $customers_query_raw = "select c.customers_id, c.customers_gender, c.customers_lastname, c.customers_firstname, c.customers_dob, c.customers_email_address, c.customers_cgars_plus, c.customers_cgars_plus_active, c.customers_points_review, cpm.member_date_created, cpm.member_current_annual_spend from " . TABLE_CUSTOMERS . " c left join cgars_plus_members cpm on c.customers_id = cpm.customers_id " . $where_str . " group by c.customers_id order by $sort";

   $customers_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $customers_query_raw, $customers_query_numrows);
   $customers_query = tep_db_query($customers_query_raw);

   while ($customers = tep_db_fetch_array($customers_query)) {

      if ((!isset($_GET['cID']) || (isset($_GET['cID']) && ($_GET['cID'] == $customers['customers_id']))) && !isset($cInfo)) {
       $cInfo_array = $customers;
       $cInfo = new objectInfo($cInfo_array);
      }

      if (isset($cInfo) && is_object($cInfo) && ($customers['customers_id'] == $cInfo->customers_id)) {
        echo '<tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id . '&action=edit') . '\'">' . "\n";
      } else {
        echo '<tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID')) . 'cID=' . $customers['customers_id']) . '\'">' . "\n";
      }
?>
                <td class="dataTableContent"><?php echo '<a href="' . tep_href_link(FILENAME_ORDERS, 'cID=' . $cInfo->customers_id) . '">' . tep_image(DIR_WS_ICONS . 'preview.gif', ICON_PREVIEW) . '</a>&nbsp;' . $customers['customers_lastname']; ?></td>
                <td class="dataTableContent"><?php echo $customers['customers_firstname']; ?></td>
                <td class="dataTableContent" align="center"><?php if($customers['customers_cgars_plus_active'] > 0){ echo 'Yes'; } ?></td>
                <td class="dataTableContent" align="center"><?php echo number_format($customers['customers_cgars_plus'], CGARS_PLUS_POINTS_DECIMAL_PLACES); ?></td>
                <td class="dataTableContent" align="center"><?php if ($customers['customers_cgars_plus'] > 0) echo $currencies->format($customers['customers_cgars_plus'] * CGARS_PLUS_REDEEM_POINT_VALUE); ?></td>
                <td class="dataTableContent" align="center">
                  <?php 
                  if ($customers['customers_cgars_plus_active'] > 0){ 
                   if($customers['customers_cgars_plus_active'] == 1){ echo '<div class="cp_bronze">B</div>'; }
                   if($customers['customers_cgars_plus_active'] == 2){ echo '<div class="cp_silver">S</div>'; }
                   if($customers['customers_cgars_plus_active'] == 3){ echo '<div class="cp_gold">G</div>'; } 
                  }
                  ?>       
                </td>
                <td class="dataTableContent" align="right"><?php echo tep_date_short($customers['member_date_created']); ?></td>
				        <td class="dataTableContent" align="right"><?php echo $currencies->format($customers['member_current_annual_spend']); ?></td>
				        <td class="dataTableContent" align="center"><?php if ($customers['customers_points_review'] > 0){ echo 'Yes'; } else { echo 'No'; } ?></td>
                <td class="dataTableContent" align="right"><?php if (isset($cInfo) && is_object($cInfo) && ($customers['customers_id'] == $cInfo->customers_id)) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif', ''); } else { echo '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID')) . 'cID=' . $customers['customers_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              </tr>
<?php
    }
?>
              <tr>
                <td colspan="9"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText" valign="top"><?php echo $customers_split->display_count($customers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_CUSTOMERS); ?></td>
                    <td class="smallText" align="right"><?php echo $customers_split->display_links($customers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y', 'cID'))); ?></td>
                  </tr>
<?php
    if (isset($_GET['search']) && tep_not_null($_GET['search'])) {
?>
                  <tr>
                    <td align="right" colspan="2"></td>
                  </tr>
<?php
    }
?>
                </table>
<?php

   $tustomers_query = tep_db_query("select sum(c.customers_cgars_plus) as points_total from " . TABLE_CUSTOMERS . " c left join " . TABLE_ADDRESS_BOOK . " ab on c.customers_id = ab.customers_id and c.customers_default_address_id = ab.address_book_id " . $where_str);
   $tustomers = tep_db_fetch_array($tustomers_query);

   echo '<div class="main"><strong>' . $currencies->format($tustomers['points_total'] * CGARS_PLUS_REDEEM_POINT_VALUE) . ' Total Customer C.Gars Plus Points Value</strong></div>';

?>
				</td>
              </tr>
            </table></td>
<?php
  $heading = array();
  $contents = array();

  switch ($action) {
    case 'addpoints':
      $heading[] = array('text' => '<b>' . $cInfo->customers_firstname . ' ' . $cInfo->customers_lastname . '</b>');

      $contents = array('form' => tep_draw_form('customers', FILENAME_CGARS_PLUS, tep_get_all_get_params() . 'cID=' . $cInfo->customers_id . '&action=addconfirm'));
      
      $value_field = '<b>'. TEXT_ADD_POINTS . '</b><br>'. TEXT_ADD_POINTS_LONG . '<br><br>' . TEXT_POINTS_TO_ADD . '<br>'. tep_draw_input_field('points_to_add', '' , 'onBlur="validate(this)"');
      $contents[] = array('text' => $value_field);

      $value_field = TEXT_COMMENT. '<br>'. tep_draw_input_field('comment', 0);
      $contents[] = array('text' => $value_field);

      $contents[] = array('text' => tep_draw_checkbox_field('notify', '', true) . ' ' . TEXT_NOTIFY_CUSTOMER);

	  if($cInfo->customers_points_review != '1'){
	   $contents[] = array('text' => tep_draw_checkbox_field('add_review') . ' Mark as review placed on Trustpilot (please also enter points above)');
	  } else {
	   $contents[] = array('text' => '<br />Trustpilot Points already rewarded');
	  }
      $contents[] = array('text' => tep_draw_hidden_field('customers_firstname', $cInfo->customers_firstname) . tep_draw_hidden_field('customers_lastname', $cInfo->customers_lastname) . tep_draw_hidden_field('customers_gender', $cInfo->customers_gender) . tep_draw_hidden_field('customers_email_address', $cInfo->customers_email_address));

      $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_add_points.gif', BUTTON_TEXT_ADD_POINTS) . ' <a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      break;
    case 'deletepoints':
      $heading[] = array('text' => '<b>' . $cInfo->customers_firstname . ' ' . $cInfo->customers_lastname . '</b>');

      $contents = array('form' => tep_draw_form('customers', FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id . '&action=delconfirm'));
      
      $value_field = '<b>'. TEXT_DELETE_POINTS . '</b><br>'. TEXT_DELETE_POINTS_LONG . '<br><br>' . TEXT_POINTS_TO_DELETE . '<br>'. tep_draw_input_field('points_to_delete', '' , 'onBlur="validate(this)"');
      $contents[] = array('text' => $value_field);
      
      $value_field = TEXT_COMMENT. '<br>'. tep_draw_input_field('comment', 0);
      $contents[] = array('text' => $value_field);

      $contents[] = array('text' => tep_draw_checkbox_field('notify', '', true) . ' ' . TEXT_NOTIFY_CUSTOMER);

      $contents[] = array('align' => 'center', 'text' => tep_draw_hidden_field('customers_firstname', $cInfo->customers_firstname) . tep_draw_hidden_field('customers_lastname', $cInfo->customers_lastname) . tep_draw_hidden_field('customers_gender', $cInfo->customers_gender) . tep_draw_hidden_field('customers_email_address', $cInfo->customers_email_address));

      $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete_points.gif', BUTTON_TEXT_DELETE_POINTS) . ' <a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      break;
      case 'signup':
        $heading[] = array('text' => '<b>' . $cInfo->customers_firstname . ' ' . $cInfo->customers_lastname . '</b>');
  
        $contents = array('form' => tep_draw_form('customers', FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id . '&action=signupconfirm'));
        
        $contents[] = array('text' => '<b>Do you wish to sign this customer up to CGars Plus?</b>');
  
        $contents[] = array('text' => tep_draw_checkbox_field('bonus', '', true) . ' send welcome email?');
  
        $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_confirm.gif', 'Confirm Sign Up') . ' <a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      break;
    default:
      if (isset($cInfo) && is_object($cInfo)) {
        $heading[] = array('text' => '<b>' . $cInfo->customers_firstname . ' ' . $cInfo->customers_lastname . '</b>');

    if ($cInfo->customers_cgars_plus_active > 0) {
        $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id . '&action=addpoints') . '">' . tep_image_button('button_add_points.gif', BUTTON_TEXT_ADD_POINTS) . '</a> <a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id . '&action=deletepoints') . '">' . tep_image_button('button_delete_points.gif', BUTTON_TEXT_DELETE_POINTS) . '</a><br /><br /><a href="' . tep_href_link(FILENAME_ORDERS, 'cID=' . $cInfo->customers_id) . '">' . tep_image_button('button_orders.gif', IMAGE_ORDERS) . '</a>');
    } else {
        $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_CGARS_PLUS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->customers_id . '&action=signup') . '">' . tep_image_button('button_sign_up.gif', 'Sign up customer') . '</a><br /><br /><a href="' . tep_href_link(FILENAME_ORDERS, 'cID=' . $cInfo->customers_id) . '">' . tep_image_button('button_orders.gif', IMAGE_ORDERS) . '</a>');
    }
      }
      break;
  }

  if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    echo '<td width="25%" valign="top">' . "\n";

    $box = new box;
    echo $box->infoBox($heading, $contents);

    echo '</td>' . "\n";
  }
?>
          </tr>
        </table></td>
      </tr>
    </table>
<?php
  require(DIR_WS_INCLUDES . 'template_bottom.php');
  require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
